# Malda Protocol - Reward Manipulation через Transfer Timing

## Description

Система наград в протоколе Malda содержит критическую уязвимость в функции `_notifySupplier()` контракта RewardDistributor.sol. При transfer операциях mToken'ов награды для получателя рассчитываются неправильно, позволяя пользователям получать незаслуженные награды за периоды когда они не владели соответствующими токенами.

Проблема возникает из-за неправильной последовательности операций: баланс токенов получателя читается ПОСЛЕ выполнения transfer'а, но награды рассчитываются за весь период с последнего взаимодействия получателя с протоколом.

## Vulnerability Details

### Механизм уязвимости

В функции `_notifySupplier()` награды рассчитываются по формуле:
```solidity
uint256 supplierTokens = ImToken(mToken).balanceOf(supplier);
uint256 supplierDelta = mul_(supplierTokens, deltaIndex);
```

Где:
- `supplierTokens` - текущий баланс пользователя (читается ПОСЛЕ transfer'а)
- `deltaIndex = supplyIndex - supplierIndex` - накопленные награды с последнего взаимодействия

### Последовательность операций при transfer:

1. `mToken.transfer(recipient, amount)` вызывает `beforeMTokenTransfer()`
2. `beforeMTokenTransfer()` обновляет награды для отправителя и получателя
3. `_notifySupplier(recipient)` вызывается для получателя
4. `balanceOf(recipient)` возвращает УЖЕ увеличенный баланс
5. Награды рассчитываются как `новый_баланс × период_без_владения`

### Сценарий эксплуатации:

```
Начальное состояние:
- Пользователь B взаимодействовал с протоколом 30 дней назад
- supplierIndex[B] = 1000 (старое значение)
- Текущий supplyIndex = 2000 (накопились награды)
- Баланс B = 0 mTokens

Атака:
1. Пользователь A делает transfer(B, 1000 mTokens)
2. При transfer вызывается _notifySupplier(B):
   - deltaIndex = 2000 - 1000 = 1000 (30 дней наград)
   - supplierTokens = balanceOf(B) = 1000 (новые токены)
   - supplierDelta = 1000 × 1000 = 1,000,000 наград
3. Пользователь B получает огромную сумму наград за период неучастия
```

### Защитный механизм и его ограничения:

Код содержит защиту для новых пользователей:
```solidity
if (supplierIndex == 0 && supplyIndex >= REWARD_INITIAL_INDEX) {
    supplierIndex = REWARD_INITIAL_INDEX;
}
```

Однако эта защита работает только для пользователей с `supplierIndex == 0` (новые пользователи). Существующие пользователи с историей взаимодействий остаются уязвимыми.

### Затронутые функции:

- `RewardDistributor._notifySupplier()`
- `Operator.beforeMTokenTransfer()`
- `mToken.transfer()` и `mToken.transferFrom()`

## Impact

### Финансовые последствия:
- **Незаслуженные награды**: Пользователи могут получать награды за периоды неучастия в протоколе
- **Истощение reward pool**: Неправильное распределение может привести к преждевременному исчерпанию наград
- **Несправедливое распределение**: Активные пользователи получают меньше наград из-за манипуляций

### Технические последствия:
- **Нарушение экономической модели**: Система стимулов работает неправильно
- **Потеря доверия**: Пользователи могут потерять доверие к справедливости протокола
- **Арбитражные возможности**: Создаются возможности для извлечения прибыли без риска

### Severity Assessment:
**Medium Severity** - Нарушение механики распределения наград без прямой потери TVL, но с существенным влиянием на экономику протокола.

### Рекомендации по исправлению:

1. **Читать баланс ДО transfer'а**: Передавать текущий баланс как параметр в `_notifySupplier()`
2. **Использовать переданные параметры**: Использовать `transferTokens` для корректного расчета
3. **Дополнительная валидация**: Добавить проверки на корректность расчета наград
4. **Тестирование**: Создать comprehensive тесты для различных сценариев transfer'ов

### Пример исправления:

```solidity
function _notifySupplier(address rewardToken, address mToken, address supplier, uint256 supplierTokensBefore) private {
    // Использовать баланс ДО transfer'а для корректного расчета
    uint256 supplierDelta = mul_(supplierTokensBefore, deltaIndex);
    // ...
}
```
у тебя не понятный пример давай я тебе объясню а ты скажешь так и не так 1) пользователь B имел например 1000 токенов 2) пользователь А собрал свои комиссии 3) сделал трансфер своих 1000 токенов к B 4) в итоге баланс пользователя B проверился вышло 2000 и эти 2000 он умножил на deltaIndex который нужно было умножить только за удержание 1000 токенов и в итоге получил двойную награду я прав? 

1) Пользователь B имеет 1000 mTokens (уже долгое время)
2) Пользователь A собирает свои награды через claim()
3) Пользователь A делает transfer(B, 1000) своих токенов
4) При transfer вызывается _notifySupplier(B):
   - deltaIndex = период с последнего взаимодействия B
   - supplierTokens = balanceOf(B) = 2000 (старые 1000 + новые 1000)
   - Награды = 2000 × deltaIndex
5) Пользователь B получает ДВОЙНУЮ награду!