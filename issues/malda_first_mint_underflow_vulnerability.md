# Malda Protocol - First Mint Underflow Attack Vulnerability

## Description

Критическая уязвимость в функции `__mint()` контракта `mToken.sol`, которая позволяет любому пользователю заблокировать инициализацию новых рынков через underflow атаку при первом mint'е.

## Vulnerability Details

### Проблемный код

В файле `malda-lending/src/mToken/mToken.sol`, строки 699-707:

```solidity
uint256 mintTokens = div_(actualMintAmount, exchangeRate);
require(mintTokens >= minAmountOut, mt_MinAmountNotValid());

// avoid exchangeRate manipulation
if (totalSupply == 0) {
    totalSupply = 1000;
    accountTokens[address(0)] = 1000;
    mintTokens -= 1000;  // ❌ UNDERFLOW если mintTokens < 1000!
}
```

### Корневая причина

1. **Отсутствие проверки минимального размера** первого mint'а
2. **Безусловное вычитание 1000 токенов** без проверки что `mintTokens >= 1000`
3. **Solidity 0.8.28** автоматически ревертит транзакции при underflow

### Сценарий атаки

1. **Атакующий** делает первый mint с минимальной суммой (например, 1 wei underlying токена)
2. При стандартном `exchangeRate = 1e18`, получается `mintTokens = 1`
3. Код пытается выполнить `mintTokens -= 1000`, что дает `1 - 1000 = underflow`
4. **Транзакция ревертится** из-за встроенной защиты Solidity от underflow
5. **Рынок остается неинициализированным** и не может быть запущен

### Подтверждение отсутствия защиты

Анализ кода показал что **НЕТ** минимальных требований к размеру mint'а:

- **mToken.sol**: Только проверка `mintTokens >= minAmountOut` (пользовательский параметр)
- **Operator.sol**: Только проверки паузы, blacklist и market listing
- **mErc20Host.sol**: Только проверка `amount > 0`

## Impact

### Критичность: HIGH

1. **DoS инициализации рынков** - любой может предотвратить запуск новых рынков
2. **Griefing attack** - атакующий может нанести ущерб протоколу минимальными затратами
3. **Нарушение бизнес-логики** - протокол не может развертывать новые рынки
4. **Экономический ущерб** - задержка запуска рынков может привести к потере пользователей и TVL

### Примеры атаки

```solidity
// Атакующий может заблокировать рынок одной транзакцией:
mToken.mint(1 wei, 0); // mintTokens = 1, underflow при вычитании 1000
```

### Затраты атаки

- **Минимальные**: 1 wei underlying токена + gas fees
- **Максимальный ущерб**: Полная блокировка инициализации рынка

## Рекомендации по исправлению

### Вариант 1: Проверка минимального размера

```solidity
if (totalSupply == 0) {
    require(mintTokens >= 1000, "First mint must be >= 1000 tokens");
    totalSupply = 1000;
    accountTokens[address(0)] = 1000;
    mintTokens -= 1000;
}
```

### Вариант 2: Безопасное вычитание

```solidity
if (totalSupply == 0) {
    totalSupply = 1000;
    accountTokens[address(0)] = 1000;
    mintTokens = mintTokens > 1000 ? mintTokens - 1000 : 0;
}
```

### Вариант 3: Административная инициализация

```solidity
bool public isInitialized;

modifier onlyAfterInit() {
    require(isInitialized, "Market not initialized");
    _;
}

function initializeMarket(uint256 initialAmount) external onlyAdmin {
    require(!isInitialized && totalSupply == 0, "Already initialized");
    require(initialAmount >= 1000, "Initial amount too small");
    
    totalSupply = 1000;
    accountTokens[address(0)] = 1000;
    isInitialized = true;
    
    // Выполнить первый mint для admin'а
    _mint(msg.sender, msg.sender, initialAmount, 0, true);
}
```

## Связанные уязвимости

Эта проблема связана с другой найденной уязвимостью:
- **Bypass minAmountOut при первом mint'е** - проверка `minAmountOut` происходит ДО вычитания 1000 токенов

## Заключение

Уязвимость First Mint Underflow Attack представляет критическую угрозу для протокола Malda, позволяя любому пользователю заблокировать инициализацию новых рынков минимальными затратами. Необходимо немедленное исправление через добавление проверки минимального размера первого mint'а или изменение логики инициализации рынков.
