# Malda Protocol - Gas Helper Bypass Vulnerability

## Description

Уязвимость в функции `checkHostToExtension()` библиотеки `CommonLib.sol`, которая позволяет обойти оплату gas fees для cross-chain операций когда `gasHelper` не настроен или установлен в `address(0)`.

## Vulnerability Details

### Проблемный код

В файле `malda-lending/src/libraries/CommonLib.sol`, функция `checkHostToExtension()`:

```solidity
function checkHostToExtension(
    uint256 amount,
    uint32 dstChainId,
    uint256 msgValue,
    mapping(uint32 => bool) storage allowedChains,
    IGasFeesHelper gasHelper
) internal view {
    if (amount == 0) revert AmountNotValid();
    if (!allowedChains[dstChainId]) revert ChainNotValid();

    uint256 requiredGas = address(gasHelper) != address(0) ? gasHelper.gasFees(dstChainId) : 0;
    //                                                                                        ↑ ПРОБЛЕМА!
    if (msgValue < requiredGas) revert NotEnoughGasFee();
}
```

### Корневая причина

1. **Отсутствие обязательной проверки gasHelper** - если `gasHelper = address(0)`, то `requiredGas = 0`
2. **Любой `msg.value >= 0` проходит проверку** - включая `msg.value = 0`
3. **Нет минимальной базовой комиссии** для cross-chain операций

### Сценарий эксплуатации

**1. Условие для эксплуатации:**
```solidity
// gasHelper не настроен или обнулен:
mErc20Host.gasHelper == address(0)
```

**2. Атакующий может выполнять бесплатные cross-chain операции:**
```solidity
// Бесплатный borrow на 1M USDC в Ethereum:
mErc20Host.performExtensionCall{value: 0}(
    2,           // actionType = borrow
    1000000e6,   // 1M USDC
    1            // dstChainId = Ethereum
);

// Бесплатный withdraw на любую сумму:
mErc20Host.performExtensionCall{value: 0}(
    1,           // actionType = withdraw  
    500000e6,    // 500K USDC
    42161        // dstChainId = Arbitrum
);
```

**3. Логика обхода:**
```solidity
// В checkHostToExtension():
uint256 requiredGas = address(0) != address(0) ? gasHelper.gasFees(dstChainId) : 0;
//                    ↑ false                                                    ↑ = 0

if (0 < 0) revert NotEnoughGasFee();  // false, проверка проходит
```

### Когда возможна эксплуатация

**1. Неправильная инициализация:**
```solidity
// Контракт развернут без настройки gasHelper
mErc20Host.gasHelper == address(0)  // По умолчанию
```

**2. Административная ошибка:**
```solidity
// Админы случайно обнуляют gasHelper
mErc20Host.setGasHelper(address(0));
```

**3. Обновление контракта:**
```solidity
// При обновлении забыли перенастроить gasHelper
```

## Impact

### Критичность: MEDIUM

1. **Экономические потери протокола:**
   - Протокол не получает оплату за cross-chain операции
   - Bridge fees и relayer costs не покрываются
   - Потенциальные убытки на каждой cross-chain транзакции

2. **DoS через спам операций:**
   - Атакующий может создать множество бесплатных cross-chain операций
   - Перегрузка relayer инфраструктуры
   - Возможная деградация сервиса

3. **Нарушение экономической модели:**
   - Cross-chain операции должны быть платными
   - Бесплатные операции нарушают баланс протокола
   - Несправедливое преимущество для некоторых пользователей

### Примеры атаки

**Массовый спам:**
```solidity
// Атакующий может создать 1000+ бесплатных операций:
for (uint i = 0; i < 1000; i++) {
    mErc20Host.performExtensionCall{value: 0}(2, 1000e6, 1);
}
```

**Крупные операции без комиссий:**
```solidity
// Займ на миллионы долларов без gas fees:
mErc20Host.performExtensionCall{value: 0}(2, 10000000e6, 1); // $10M USDC
```

### Затраты атаки

- **Минимальные**: Только gas fees в исходной сети (Linea)
- **Максимальный ущерб**: Неограниченные бесплатные cross-chain операции

## Рекомендации по исправлению

### Вариант 1: Обязательная настройка gasHelper

```solidity
function checkHostToExtension(
    uint256 amount,
    uint32 dstChainId,
    uint256 msgValue,
    mapping(uint32 => bool) storage allowedChains,
    IGasFeesHelper gasHelper
) internal view {
    if (amount == 0) revert AmountNotValid();
    if (!allowedChains[dstChainId]) revert ChainNotValid();
    
    require(address(gasHelper) != address(0), "GasHelper not configured");
    uint256 requiredGas = gasHelper.gasFees(dstChainId);
    
    if (msgValue < requiredGas) revert NotEnoughGasFee();
}
```

### Вариант 2: Минимальная базовая комиссия

```solidity
function checkHostToExtension(
    uint256 amount,
    uint32 dstChainId,
    uint256 msgValue,
    mapping(uint32 => bool) storage allowedChains,
    IGasFeesHelper gasHelper
) internal view {
    if (amount == 0) revert AmountNotValid();
    if (!allowedChains[dstChainId]) revert ChainNotValid();
    
    uint256 requiredGas = address(gasHelper) != address(0) 
        ? gasHelper.gasFees(dstChainId) 
        : 0.001 ether; // Минимальная комиссия
    
    if (msgValue < requiredGas) revert NotEnoughGasFee();
}
```

### Вариант 3: Отключение cross-chain операций

```solidity
function checkHostToExtension(
    uint256 amount,
    uint32 dstChainId,
    uint256 msgValue,
    mapping(uint32 => bool) storage allowedChains,
    IGasFeesHelper gasHelper
) internal view {
    if (amount == 0) revert AmountNotValid();
    if (!allowedChains[dstChainId]) revert ChainNotValid();
    
    if (address(gasHelper) == address(0)) {
        revert("Cross-chain operations disabled - gasHelper not configured");
    }
    
    uint256 requiredGas = gasHelper.gasFees(dstChainId);
    if (msgValue < requiredGas) revert NotEnoughGasFee();
}
```

### Вариант 4: Административные проверки

```solidity
// В mErc20Host.sol:
function setGasHelper(address _helper) external onlyAdmin {
    require(_helper != address(0), "GasHelper cannot be zero address");
    gasHelper = IGasFeesHelper(_helper);
}

// Добавить проверку в performExtensionCall:
function performExtensionCall(uint256 actionType, uint256 amount, uint32 dstChainId) 
    external payable override 
{
    require(address(gasHelper) != address(0), "GasHelper not configured");
    CommonLib.checkHostToExtension(amount, dstChainId, msg.value, allowedChains, gasHelper);
    // ...
}
```

## Связанные проблемы

1. **Отсутствие валидации в setGasHelper()** - можно установить address(0)
2. **Нет проверок при инициализации** контракта
3. **Отсутствие emergency pause** для cross-chain операций

## Заключение

Уязвимость Gas Helper Bypass позволяет обойти оплату gas fees для cross-chain операций когда gasHelper не настроен. Хотя это не приводит к прямой потере средств пользователей, может нанести значительный экономический ущерб протоколу и нарушить работу cross-chain инфраструктуры.

Необходимо добавить обязательную проверку настройки gasHelper или установить минимальную базовую комиссию для всех cross-chain операций.
