# Malda Protocol - Comprehensive Security Analysis

## Введение

Malda - это первый DeFi протокол построенный на zkCoprocessor технологии для создания унифицированных пулов ликвидности в экосистеме Ethereum. Протокол решает проблему фрагментации ликвидности между различными L2 сетями через zero-knowledge доказательства.

## 1. Ключевые пользовательские сценарии

### 1.1 Типы транзакций

Malda поддерживает 3 типа транзакций с различными жизненными циклами:

1. **Linea native transactions** - обычные операции как в legacy lending протоколах
2. **Deposit type transactions** (Supply, Repay) - от Extension к Host с zkProof верификацией  
3. **Withdraw type transactions** (Withdraw, Borrow) - от Host к Extension с zkProof авторизацией

### 1.2 Основные пользовательские flow'ы

**Supply Flow:**
- **Host Chain**: Прямой вызов `__mint()` → немедленное получение mTokens
- **Extension Chain**: `supplyOnHost()` → генерация zkProof → `mintExternal()` на Host

**Borrow Flow:**
- **Host Chain**: Прямой вызов `__borrow()` → немедленное получение токенов
- **Extension Chain**: `performExtensionCall(actionType=2)` → генерация zkProof → `outHere()` на Extension

**Withdraw Flow:**
- **Host Chain**: Прямой вызов `__redeem()` → немедленное получение underlying токенов
- **Extension Chain**: `performExtensionCall(actionType=1)` → генерация zkProof → `outHere()` на Extension

**Repay Flow:**
- **Host Chain**: Прямой вызов `__repayBorrow()` → немедленное погашение долга
- **Extension Chain**: `supplyOnHost()` → генерация zkProof → `repayExternal()` на Host

**Liquidation Flow:**
- Только на Host Chain через `liquidateExternal()` с zkProof верификацией позиций

## 2. Точки входа (Entry Points)

### 2.1 Host Chain (Linea) - mErc20Host.sol

**Прямые операции (без zkProof):**
```solidity
function __mint(uint256 mintAmount, address receiver, uint256 minAmountOut) external
function __redeem(uint256 redeemTokens, address receiver, uint256 minAmountOut) external  
function __borrow(uint256 borrowAmount, address receiver) external
function __repayBorrow(uint256 repayAmount, address borrower) external
```

**Кросс-чейн операции (с zkProof верификацией):**
```solidity
function mintExternal(bytes calldata journalData, bytes calldata seal, uint256[] calldata mintAmount, uint256[] calldata minAmountsOut, address receiver) external
function repayExternal(bytes calldata journalData, bytes calldata seal, uint256[] calldata repayAmount, address receiver) external
function liquidateExternal(bytes calldata journalData, bytes calldata seal, address[] calldata userToLiquidate, uint256[] calldata liquidateAmount, address[] calldata collateral, address receiver) external
function performExtensionCall(uint256 actionType, uint256 amount, uint32 dstChainId) external payable
```

**Административные функции:**
```solidity
function extractForRebalancing(uint256 amount) external // REBALANCER роль
function updateAllowedChain(uint32 _chainId, bool _status) external // Admin
function setMigrator(address _migrator) external // Admin
```

### 2.2 Extension Chains - mTokenGateway.sol

**Пользовательские функции:**
```solidity
function supplyOnHost(uint256 amount, address receiver, bytes4 lineaSelector) external payable
function outHere(bytes calldata journalData, bytes calldata seal, uint256[] calldata amounts, address receiver) external
```

**Административные функции:**
```solidity
function setPaused(OperationType _type, bool _status) external // Owner
function setAllowedCaller(address caller, bool status) external // Owner
function setGasFee(uint256 _gasFee) external // Owner
```

## 3. Архитектура Host vs Extension

### 3.1 Host Chain (Linea) - Центральный Ledger

**Роль и ответственности:**
- **Основной ledger протокола** - хранит полную отчетность всех операций
- **Глобальные балансы** - отслеживает позиции пользователей со всех chains
- **Процентные ставки** - рассчитывает на основе глобальной утилизации
- **zkProof верификация** - проверяет доказательства от Extension chains
- **Операции withdraw/borrow** - инициирует выводы средств на Extension chains

**Ключевые компоненты:**
- `mErc20Host.sol` - основной контракт рынка
- `Operator.sol` - логика протокола и risk management
- `ZkVerifier.sol` - верификация RISC Zero proof'ов

### 3.2 Extension Chains (Base, Optimism, etc.) - Точки входа

**Роль и ответственности:**
- **Точки входа для пользователей** - не содержат бизнес-логику протокола
- **Прием депозитов** - принимают токены от пользователей
- **Генерация zkProof** - для операций supply/repay через Sequencer
- **Выдача средств** - по верифицированным zkProof от Host chain

**Ключевые компоненты:**
- `mTokenGateway.sol` - основной контракт Extension chain
- Аккумулятивные счетчики `accAmountIn/Out` для tracking операций

### 3.3 Взаимодействие Host ↔ Extension

**Deposit Flow (Extension → Host):**
1. Пользователь вызывает `supplyOnHost()` на Extension
2. Extension принимает токены и обновляет `accAmountIn[receiver]`
3. Эмитируется событие с `lineaSelector` для Sequencer
4. Sequencer генерирует zkProof и вызывает `mintExternal()` на Host
5. Host верифицирует proof и обновляет глобальные балансы

**Withdraw Flow (Host → Extension):**
1. Пользователь вызывает `performExtensionCall()` на Host
2. Host проверяет возможность операции и эмитирует событие
3. Sequencer генерирует zkProof и вызывает `outHere()` на Extension
4. Extension верифицирует proof и выдает токены пользователю
5. Обновляется `accAmountOut[sender]` на Extension

## 4. Генерация zkProof

### 4.1 Архитектура ZK Coprocessor

**Компоненты системы:**
- **RISC Zero zkVM**: Выполнение guest программ для генерации proof'ов
- **Guest Program**: `get_proof_data.rs` - основная логика верификации состояния
- **Steel Framework**: Верификация состояния различных блокчейнов
- **Sequencer**: Централизованный сервис для автоматической генерации proof'ов

### 4.2 Функции генерации zkProof

**В Rust коде (malda-zk-coprocessor):**
```rust
// Основная функция генерации proof'ов через Bonsai SDK
pub async fn get_proof_data_prove_sdk(
    users: Vec<Vec<Address>>,
    markets: Vec<Vec<Address>>, 
    target_chain_ids: Vec<Vec<u64>>,
    chain_ids: Vec<u64>,
    l1_inclusion: bool,
) -> Result<MaldaProveInfo, Error>

// Локальная генерация proof'ов
pub async fn get_proof_data_prove(
    users: Vec<Vec<Address>>,
    markets: Vec<Vec<Address>>,
    target_chain_ids: Vec<Vec<u64>>,
    chain_ids: Vec<u64>,
    l1_inclusion: bool,
) -> Result<ProveInfo, Error>

// Подготовка данных для on-chain submission
let journal = Bytes::from(proof_info.receipt.journal.bytes);
let seal = risc0_ethereum_contracts::encode_seal(&receipt);
```

### 4.3 Структура Journal данных

**В Solidity (mTokenProofDecoderLib.sol):**
```solidity
struct JournalEntry {
    address sender;        // 20 bytes - адрес отправителя операции
    address market;        // 20 bytes - адрес рынка (mToken контракт)
    uint256 accAmountIn;   // 32 bytes - накопленная сумма входящих средств
    uint256 accAmountOut;  // 32 bytes - накопленная сумма исходящих средств
    uint32 chainId;        // 4 bytes - ID исходной сети
    uint32 dstChainId;     // 4 bytes - ID целевой сети
    bool L1inclusion;      // 1 byte - флаг включения L1 данных для reorg protection
}
// Общий размер: 113 bytes (ENTRY_SIZE константа)
```

**Функции декодирования:**
```solidity
function decodeJournal(bytes memory journalData) internal pure returns (
    address sender, address market, uint256 accAmountIn, uint256 accAmountOut,
    uint32 chainId, uint32 dstChainId, bool L1inclusion
)

function encodeJournal(...) internal pure returns (bytes memory)
```

### 4.4 Верификация zkProof

**В контрактах Host и Extension:**
```solidity
function _verifyProof(bytes calldata journalData, bytes calldata seal) internal view {
    require(journalData.length > 0, JournalNotValid());
    
    // Декодирование массива journal'ов
    bytes[] memory journals = _decodeJournals(journalData);
    
    // Проверка L1Inclusion флага для non-sequencer пользователей
    bool isSequencer = _isAllowedFor(msg.sender, _getProofForwarderRole()) 
        || _isAllowedFor(msg.sender, _getBatchProofForwarderRole());
    
    if (!isSequencer) {
        for (uint256 i = 0; i < journals.length; i++) {
            (,,,,,, bool L1Inclusion) = mTokenProofDecoderLib.decodeJournal(journals[i]);
            if (!L1Inclusion) revert L1InclusionRequired();
        }
    }
    
    // Верификация через RISC Zero verifier
    verifier.verifyInput(journalData, seal);
}
```

## 5. Критические точки безопасности

### 5.1 Sink Points (Места потенциальных потерь активов)

**1. zkProof Верификация - Критический компонент**
- **Риск**: Подделка или обход zkProof может привести к несанкционированному минту/выводу
- **Защита**: Обязательная верификация через RISC Zero verifier
- **Уязвимость**: Sequencer роли (`PROOF_FORWARDER`, `PROOF_BATCH_FORWARDER`) могут пропускать L1Inclusion проверки

**2. Cross-chain Balance Tracking**
- **Риск**: Рассинхронизация балансов между Host и Extension chains
- **Защита**: Аккумулятивные счетчики `accAmountIn/Out` в journal данных
- **Уязвимость**: Возможность replay атак при повторном использовании proof'ов

**3. Rebalancing Механизм**
- **Риск**: Неконтролируемый вывод средств через `extractForRebalancing()`
- **Защита**: Ограничения через `checkOutflowVolumeLimit()` в Operator
- **Уязвимость**: REBALANCER роль имеет прямой доступ к underlying токенам

**4. Outflow Volume Controls**
- **Риск**: Обход лимитов на вывод средств
- **Защита**: `limitPerTimePeriod` и `cumulativeOutflowVolume` в Operator
- **Уязвимость**: Проверка происходит только на уровне отдельного рынка

### 5.2 Критические роли и разрешения

**Высокорисковые роли:**
```solidity
PROOF_BATCH_FORWARDER - может пропускать zkProof верификацию для batch операций
REBALANCER - может извлекать средства для rebalancing через extractForRebalancing()
GUARDIAN_BRIDGE - управляет мостами и rebalancing параметрами
SEQUENCER - генерирует proof'ы и может цензурировать транзакции
REBALANCER_EOA - может инициировать cross-chain transfers
```

**Административные роли:**
```solidity
Owner - полный контроль над контрактами
Admin - управление параметрами протокола
GUARDIAN_PAUSE - экстренные паузы операций
GUARDIAN_BLACKLIST - управление blacklist'ом
```

### 5.3 Потенциальные векторы атак

**1. Sequencer Centralization Risk**
- **Проблема**: Sequencer может цензурировать транзакции пользователей
- **Mitigation**: Self-sequencing возможности для пользователей
- **Риск**: Сложность self-sequencing может привести к vendor lock-in

**2. L1Inclusion Bypass**
- **Проблема**: Sequencer роли могут пропускать L1Inclusion проверки
- **Риск**: Использование устаревших или поддельных данных состояния
- **Критичность**: HIGH - может привести к double-spending

**3. Outflow Volume Limits Bypass**
- **Проблема**: Ограничения могут быть обойдены через множественные рынки
- **Риск**: Проверка происходит только на уровне отдельного рынка
- **Атака**: Координированный вывод через разные активы

**4. Cross-chain State Inconsistency**
- **Проблема**: Возможность рассинхронизации между Host и Extension
- **Риск**: Особенно критично при reorg'ах на Extension chains
- **Последствия**: Потеря средств или создание необеспеченных позиций

**5. Rebalancing Privilege Escalation**
- **Проблема**: REBALANCER роль имеет прямой доступ к underlying токенам
- **Риск**: Компрометация REBALANCER может привести к полной потере TVL
- **Защита**: Только через whitelisted bridges и destinations

### 5.4 Специфические уязвимости zkProof системы

**1. Journal Data Manipulation**
- **Проблема**: Возможность подделки `accAmountIn/Out` значений
- **Защита**: Верификация через zkVM guest program
- **Слабое место**: Зависимость от корректности RPC данных

**2. Replay Attack Vectors**
- **Проблема**: Повторное использование валидных proof'ов
- **Защита**: Аккумулятивные счетчики должны предотвращать replay
- **Риск**: Недостаточная проверка nonce/timestamp в journal

**3. Chain-specific Verification Bypass**
- **Проблема**: Различные механизмы верификации для разных chains
- **Ethereum**: Light client через beacon chain
- **OpStack**: Sequencer commitment + dispute game validation
- **Linea**: Sequencer commitment + L1 inclusion
- **Риск**: Слабейшее звено определяет общую безопасность

## 6. Рекомендации по аудиту

### 6.1 Приоритетные области для исследования

**1. zkProof Верификация (CRITICAL)**
- Проверить корректность декодирования journal данных
- Убедиться в невозможности bypass L1Inclusion проверок
- Исследовать возможность replay атак

**2. Cross-chain Balance Consistency (HIGH)**
- Проверить синхронизацию `accAmountIn/Out` между chains
- Исследовать race conditions при concurrent операциях
- Проверить handling reorg'ов на Extension chains

**3. Rebalancing Security (HIGH)**
- Проверить ограничения на `extractForRebalancing()`
- Исследовать возможность обхода outflow limits
- Проверить whitelist механизмы для bridges

**4. Role-based Access Control (MEDIUM)**
- Проверить корректность проверок ролей
- Исследовать возможность privilege escalation
- Проверить emergency pause механизмы

### 6.2 Конкретные тест-кейсы

**1. zkProof Manipulation Tests**
```solidity
// Тест на подделку journal данных
function testFakeJournalData() {
    // Создать поддельный journal с завышенными accAmountIn
    // Попытаться обойти zkProof верификацию
}

// Тест на replay атаки
function testReplayAttack() {
    // Использовать валидный proof повторно
    // Проверить защиту от double-spending
}
```

**2. Cross-chain Consistency Tests**
```solidity
// Тест на рассинхронизацию балансов
function testBalanceDesync() {
    // Симулировать reorg на Extension chain
    // Проверить consistency с Host chain
}
```

**3. Rebalancing Security Tests**
```solidity
// Тест на обход outflow limits
function testOutflowLimitBypass() {
    // Попытаться вывести больше лимита через разные рынки
    // Проверить агрегированные ограничения
}
```

### 6.3 Применение опыта CAP аудита

**Аналогии с CAP протоколом:**
- **Timing vulnerabilities**: Проверить аналогичные проблемы с withdrawal delays
- **Coverage calculations**: Исследовать корректность balance tracking
- **Interest accrual**: Проверить глобальные процентные ставки
- **Liquidation mechanics**: Проверить cross-chain liquidation logic

**Ключевые отличия:**
- **zkProof dependency**: Новый вектор атак через proof manipulation
- **Cross-chain complexity**: Больше поверхности для атак
- **Sequencer centralization**: Дополнительный trust assumption

## 7. Обнаруженные уязвимости

### 7.1 Reward Manipulation через Transfer Timing

**Description**: Система наград в RewardDistributor.sol неправильно рассчитывает награды для получателя токенов при transfer операциях, позволяя получить награды за период владения токенами другим пользователем.

**Vulnerability Details**:
В функции `_notifySupplier()` награды рассчитываются по формуле `supplierDelta = supplierTokens * deltaIndex`, где `supplierTokens` читается через `balanceOf()` ПОСЛЕ выполнения transfer'а, а `deltaIndex` представляет период с последнего взаимодействия пользователя. Это создает ситуацию где:

1. Пользователь B взаимодействовал с протоколом 30 дней назад (`supplierIndex = старое_значение`)
2. Пользователь A делает `transfer(B, tokens)`
3. При transfer вызывается `_notifySupplier(B)` который:
   - Рассчитывает `deltaIndex = текущий_supplyIndex - старый_supplierIndex` (30 дней наград)
   - Читает `supplierTokens = balanceOf(B)` (новый увеличенный баланс)
   - Начисляет награды: `новый_баланс * награды_за_30_дней`

Защитный механизм `if (supplierIndex == 0)` работает только для новых пользователей, но не защищает существующих пользователей с историей взаимодействий.

**Impact**: Пользователи могут получать незаслуженные награды за периоды когда они не владели токенами, что приводит к неправильному распределению наград и потенциальному истощению reward pool'а. Severity: Medium - нарушение механики распределения наград.
